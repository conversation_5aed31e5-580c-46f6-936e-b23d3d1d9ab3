import os
import re
from openai import OpenAI
from typing import Dict, Any

class ModelService:
    def __init__(self):
        # 在测试模式下不需要真实的API客户端
        if os.getenv("TEST_MODE") == "true":
            self.client = None
        else:
            self.client = OpenAI(
                api_key=os.getenv("OPENAI_API_KEY"),
                base_url=os.getenv("OPENAI_API_BASE", "https://api.openai.com/v1")
            )

    def _get_system_prompt(self, chapter_outline: str, criteria_text: str, review_guide: str = None) -> str:
        return f"""
# 角色
你是专业的可研报告评审专家，可以按照《可研报告编制大纲》（见下文）及《审查细则》（见下文）对用户提供的部分章节内容进行评审。

# 职责
请对提供的章节内容，根据可研报告大纲，逐一检查其对所有审查细则的符合情况，输出审查情况：（符合、基本符合、不符合），不符合情况给出具体原因。并输出结构化的JSON格式评审结果。

# 工作流程
1. 仔细阅读章节内容
2. 根据编制大纲的要求，逐一检查每个审查细则与该章节的相关性：
  - 2.1 如果不相关可输出:不适用;
  - 2.2 如果相关对需要根据审查细则进行符合性评审, 给出审查情况。
  - 2.3 审查时需要根据编制大纲中该章节要求进行符合性评审。
3. 示例：以第1章节为例，对于"审查细则1"可能输出内容包括：
  - 当章节1与"审查项1"不相关时，可输出：不适用该审查项
  - 当章节1与"审查项1"相关时，可输出：具体的审查情况，如
    * 或 章节1 基本符合
    * 或 章节1 改动了大纲标题：1.2
    * 或 章节1 投资估算编制说明应包括工程量确定的主要依据和计算原则

# 评审标准
- **符合**：章节内容完全满足审查细则要求
- **基本符合**：章节内容大部分满足要求，但有轻微不足
- **不符合**：章节内容明显不满足审查细则要求
- **不适用**：该审查细则与当前章节内容无关

# 输出格式
请严格按照以下JSON格式输出，不要添加任何其他文字：
```json
{{
  "criteria_results": [
    {{
      "criterion_id": "审查细则编号",
      "criterion_content": "审查细则内容摘要",
      "result": "符合/基本符合/不符合/不适用",
      "explanation": "具体说明原因，如果不符合请详细说明问题"
    }}
  ]
}}
```

# 注意事项
1. 必须对每个审查细则都给出评审结果
2. 评审要基于章节实际内容，不要主观臆测
3. 不符合的情况要具体说明问题所在
4. 不适用的情况要说明为什么与该章节无关
5. 输出必须是有效的JSON格式

# 当前章节编制大纲要求：
{chapter_outline if chapter_outline else '未提供该章节的大纲信息'}

# 审查指南：
{review_guide if review_guide else '未提供审查指南'}

# 所有审查细则：
{criteria_text}
        """
    def _clean_response(self, content: str) -> str:
        """清理推理模型的响应内容，去除思考标签"""
        # 去除 <think>...</think> 标签及其内容
        content = re.sub(r'<think>.*?</think>', '', content, flags=re.DOTALL)

        # 去除 <thinking>...</thinking> 标签及其内容
        content = re.sub(r'<thinking>.*?</thinking>', '', content, flags=re.DOTALL)

        # 去除多余的空行
        content = re.sub(r'\n\s*\n', '\n\n', content)

        return content.strip()

    def analyze_section_batch(self, project_name: str, section_title: str, section_content: str, all_criteria: list, chapter_outline: str = None, review_guide: str = None) -> Dict[str, Any]:
        """批量分析单个章节对所有审查细则的符合情况"""
        # 如果是测试模式，返回模拟结果
        if os.getenv("TEST_MODE") == "true":
            mock_results = []
            for i, criterion in enumerate(all_criteria):
                mock_results.append({
                    "criterion_id": criterion.get("id", f"test_{i}"),
                    "criterion_content": criterion.get("content", "测试审查细则"),
                    "result": "基本符合",
                    "explanation": f"[测试模式] 章节 {section_title} 对审查细则 {criterion.get('id', i)} 的评审结果"
                })
            return {"criteria_results": mock_results}
        # 如果章节内容为空，直接返回
        if not section_content.strip():
            empty_results = []
            for i, criterion in enumerate(all_criteria):
                empty_results.append({
                    "criterion_id": criterion.get("id", f"test_{i}"),
                    "criterion_content": criterion.get("content", "测试审查细则"),
                    "result": "不适用",
                    "explanation": f" 章节 {section_title} 内容为空"
                })
            return {"criteria_results": empty_results}

        # 构建所有审查细则的文本，过滤无效数据
        criteria_text = ""
        valid_criteria_count = 0

        for i, criterion in enumerate(all_criteria, 1):
            criterion_id = str(criterion.get('id', '')).strip()
            criterion_content = str(criterion.get('content', '')).strip()
            criterion_requirements = str(criterion.get('requirements', '')).strip()

            # 跳过空的或无效的审查细则
            if (not criterion_content or
                criterion_content in ['未指定', '未知内容', 'nan', 'NaN', '']):
                print(f"跳过无效的审查细则 {i}: ID='{criterion_id}', 内容='{criterion_content[:30]}...'")
                continue

            valid_criteria_count += 1
            criteria_text += f"""
审查细则 {valid_criteria_count}：
- 编号：{criterion_id}
- 审查范畴：{criterion_requirements if criterion_requirements and criterion_requirements != 'nan' else '未指定'}
- 审查内容：{criterion_content}
"""

        print(f"有效审查细则数量: {valid_criteria_count} / {len(all_criteria)}")

        try:
            print(f"正在调用大模型批量分析章节 [{section_title}] 对所有审查细则的符合情况...")
            print(f"章节内容长度: {len(section_content)} 字符")
            print(f"审查细则数量: {len(all_criteria)} 项")

            sys_prompt = self._get_system_prompt(chapter_outline, criteria_text, review_guide)
            response = self.client.chat.completions.create(
                model=os.getenv("MODEL_NAME", "qwq-32b"),
                messages=[
                    {"role": "system", "content": sys_prompt},
                    {"role": "user", "content": f"""
请对以下章节内容进行全面的合规性审查：
可研报告项目名称: {project_name}

章节标题：{section_title}

章节内容：
{section_content if section_content.strip() else '该章节内容为空'}

请按照系统提示中的JSON格式，对该章节逐一评审所有审查细则的符合情况。
"""}
                ],
                timeout=120  # 设置120秒超时
            )

            # 清理响应内容
            raw_content = response.choices[0].message.content
            cleaned_content = self._clean_response(raw_content)

            print(f"\n大模型批量评审响应: {cleaned_content[:300]}...\n")

            # 尝试解析JSON响应
            try:
                import json
                # 提取JSON部分
                json_start = cleaned_content.find('{')
                json_end = cleaned_content.rfind('}') + 1
                if json_start >= 0 and json_end > json_start:
                    json_str = cleaned_content[json_start:json_end]
                    result = json.loads(json_str)
                    return result
                else:
                    raise ValueError("未找到有效的JSON格式")
            except Exception as json_error:
                print(f"JSON解析失败: {json_error}")
                # 如果JSON解析失败，返回原始文本格式
                return {
                    "criteria_results": [{
                        "criterion_id": "parse_error",
                        "criterion_content": "JSON解析失败",
                        "result": "不适用",
                        "explanation": f"大模型响应格式错误，原始响应：{cleaned_content[:500]}"
                    }]
                }

        except Exception as e:
            print(f"API调用失败: {e}")
            return {
                "criteria_results": [{
                    "criterion_id": "api_error",
                    "criterion_content": "API调用失败",
                    "result": "不适用",
                    "explanation": f"API调用失败：{str(e)}"
                }]
            }

    def _prepare_report_summary(self, all_sections: dict) -> str:
        """准备报告全文摘要"""
        summary_parts = []
        for section_title, section_content in all_sections.items():
            if section_content and section_content.strip():
                # 截取每个章节的前500字符作为摘要
                content_preview = section_content.strip()[:500]
                summary_parts.append(f"【{section_title}】: {content_preview}...")
            else:
                summary_parts.append(f"【{section_title}】: (章节内容为空)")

        return "\n\n".join(summary_parts)

    def analyze_criteria_comprehensive(self, criterion: dict, report_summary: str) -> Dict[str, Any]:
        """对单个审查细则进行全文综合分析"""
        # 如果是测试模式，返回模拟结果
        if os.getenv("TEST_MODE") == "true":
            criterion_id = criterion.get("criterion_id", "unknown")
            return {
                "comprehensive_analysis": f"[测试模式] 对审查细则 {criterion_id} 的全文综合分析",
                "overall_assessment": "基本符合",
                "key_findings": [f"[测试模式] 审查细则 {criterion_id} 的关键发现"],
                "recommendations": [f"[测试模式] 审查细则 {criterion_id} 的改进建议"]
            }

        criterion_id = criterion.get("criterion_id", "unknown")
        criterion_content = criterion.get("criterion_content", "")
        section_results = criterion.get("section_results", [])

        # 构建章节分析摘要
        section_analysis_summary = []
        for result in section_results:
            section_analysis_summary.append(
                f"章节【{result['section']}】: {result['result']} - {result['explanation']}"
            )

        try:
            response = self.client.chat.completions.create(
                model=os.getenv("MODEL_NAME", "qwq-32b"),
                messages=[
                    {"role": "system", "content": """
# 角色
你是专业的可研报告评审专家，负责对审查细则进行全文综合分析。

# 任务
基于提供的报告全文摘要和各章节的初步分析结果，对单个审查细则给出全文综合评审意见。

# 分析要求
1. 综合考虑报告全文内容，不仅仅是单个章节
2. 基于各章节的分析结果，给出该审查细则的整体符合情况
3. 识别关键发现和问题
4. 提供具体的改进建议

# 输出格式
请严格按照以下JSON格式输出：
```json
{
  "comprehensive_analysis": "对该审查细则的全文综合分析，包括整体评价和关键发现",
  "overall_assessment": "符合/基本符合/不符合/不适用",
  "key_findings": [
    "关键发现1",
    "关键发现2"
  ],
  "recommendations": [
    "具体改进建议1",
    "具体改进建议2"
  ]
}
```

# 评审标准
- **符合**：报告全文完全满足该审查细则要求
- **基本符合**：报告全文大部分满足要求，但有轻微不足
- **不符合**：报告全文明显不满足该审查细则要求
- **不适用**：该审查细则与报告内容无关
"""},
                    {"role": "user", "content": f"""
请对以下审查细则进行全文综合分析：

审查细则ID：{criterion_id}
审查细则内容：{criterion_content}

各章节分析结果：
{chr(10).join(section_analysis_summary) if section_analysis_summary else '暂无章节分析结果'}

报告全文摘要：
{report_summary}

请基于以上信息，给出该审查细则的全文综合评审意见。
"""}
                ],
                timeout=120
            )

            # 清理响应内容
            raw_content = response.choices[0].message.content
            cleaned_content = self._clean_response(raw_content)

            # 解析JSON响应
            try:
                import json
                json_start = cleaned_content.find('{')
                json_end = cleaned_content.rfind('}') + 1
                if json_start >= 0 and json_end > json_start:
                    json_str = cleaned_content[json_start:json_end]
                    result = json.loads(json_str)
                    return result
                else:
                    raise ValueError("未找到有效的JSON格式")
            except Exception as json_error:
                print(f"JSON解析失败: {json_error}")
                return {
                    "comprehensive_analysis": f"综合分析失败，原始响应：{cleaned_content[:200]}...",
                    "overall_assessment": "不适用",
                    "key_findings": ["分析过程出现错误"],
                    "recommendations": ["建议重新进行分析"]
                }

        except Exception as e:
            print(f"综合分析API调用失败: {e}")
            return {
                "comprehensive_analysis": f"API调用失败：{str(e)}",
                "overall_assessment": "不适用",
                "key_findings": ["API调用失败"],
                "recommendations": ["请检查网络连接和API配置"]
            }

    def summarize_review(self, review_results: list) -> Dict[str, Any]:
        """汇总所有评审结果"""
        # 如果是测试模式，返回模拟结果
        if os.getenv("TEST_MODE") == "true":
            return {
                "summary": f"[测试模式] 总体评审意见：\n共分析了 {len(review_results)} 个章节\n总体评审结论：基本符合要求\n主要问题：无重大问题\n改进建议：建议进一步完善细节"
            }

        # 提取关键信息用于汇总
        summary_data = {
            "total_sections": len(review_results),
            "sections_with_content": 0,
            "compliance_issues": [],
            "non_compliance_issues": [],
            "not_applicable_count": 0,
            "total_criteria": 0
        }

        for section in review_results:
            if section.get("has_content", False):
                summary_data["sections_with_content"] += 1

            for analysis in section.get("analysis", []):
                summary_data["total_criteria"] += 1
                result = analysis.get("result", "不适用")
                criterion_id = analysis.get("criterion_id", "未知")
                explanation = analysis.get("explanation", "")

                if result == "不符合":
                    summary_data["non_compliance_issues"].append({
                        "section": section.get("section", "未知章节"),
                        "criterion_id": criterion_id,
                        "explanation": explanation
                    })
                elif result == "基本符合":
                    summary_data["compliance_issues"].append({
                        "section": section.get("section", "未知章节"),
                        "criterion_id": criterion_id,
                        "explanation": explanation
                    })
                elif result == "不适用":
                    summary_data["not_applicable_count"] += 1

        # 构建汇总提示词
        prompt = f"""你是专业的可研报告评审专家，请根据以下评审统计信息给出总体评审意见。

# 评审统计信息
- 总章节数：{summary_data['total_sections']}
- 有内容的章节数：{summary_data['sections_with_content']}
- 总审查细则数：{summary_data['total_criteria']}
- 不适用项目数：{summary_data['not_applicable_count']}

# 不符合项目（共{len(summary_data['non_compliance_issues'])}项）：
{chr(10).join([f"- {item['section']} - 审查细则{item['criterion_id']}: {item['explanation']}" for item in summary_data['non_compliance_issues'][:10]])}
{'...(更多项目)' if len(summary_data['non_compliance_issues']) > 10 else ''}

# 基本符合项目（共{len(summary_data['compliance_issues'])}项）：
{chr(10).join([f"- {item['section']} - 审查细则{item['criterion_id']}: {item['explanation']}" for item in summary_data['compliance_issues'][:10]])}
{'...(更多项目)' if len(summary_data['compliance_issues']) > 10 else ''}

请按以下JSON格式给出总体评审意见：
```json
{{
  "overall_conclusion": "符合/基本符合/不符合",
  "compliance_rate": "合规率百分比",
  "major_issues": [
    "主要问题1",
    "主要问题2"
  ],
  "improvement_suggestions": [
    "改进建议1",
    "改进建议2"
  ],
  "summary_text": "总体评审意见的文字描述"
}}
```

注意：
1. 基于实际统计数据进行总结
2. 重点关注不符合项目
3. 建议要具体可操作
4. 输出必须是有效的JSON格式"""

        try:
            print(f"正在调用大模型汇总评审结果...")
            print(f"共有 {len(review_results)} 个章节的评审结果")
            print(f"不符合项目: {len(summary_data['non_compliance_issues'])} 项")
            print(f"基本符合项目: {len(summary_data['compliance_issues'])} 项")

            response = self.client.chat.completions.create(
                model=os.getenv("MODEL_NAME", "qwq-32b"),
                messages=[
                    {"role": "system", "content": "你是一个专业的可研报告评审专家，请严格按照JSON格式回答。"},
                    {"role": "user", "content": prompt}
                ],
                timeout=120  # 设置120秒超时
            )

            # 清理响应内容
            raw_content = response.choices[0].message.content
            cleaned_content = self._clean_response(raw_content)

            print(f"\n大模型汇总响应: {cleaned_content[:300]}...\n")

            # 尝试解析JSON响应
            try:
                import json
                # 提取JSON部分
                json_start = cleaned_content.find('{')
                json_end = cleaned_content.rfind('}') + 1
                if json_start >= 0 and json_end > json_start:
                    json_str = cleaned_content[json_start:json_end]
                    result = json.loads(json_str)
                    return {"summary": result}
                else:
                    raise ValueError("未找到有效的JSON格式")
            except Exception as json_error:
                print(f"JSON解析失败: {json_error}")
                # 如果JSON解析失败，返回原始文本格式
                return {
                    "summary": {
                        "overall_conclusion": "不适用",
                        "compliance_rate": "0%",
                        "major_issues": ["JSON解析失败"],
                        "improvement_suggestions": ["请检查大模型响应格式"],
                        "summary_text": f"汇总失败，原始响应：{cleaned_content[:500]}"
                    }
                }

        except Exception as e:
            print(f"API调用失败: {e}")
            return {
                "summary": {
                    "overall_conclusion": "不适用",
                    "compliance_rate": "0%",
                    "major_issues": [f"API调用失败：{str(e)}"],
                    "improvement_suggestions": ["请检查网络连接和API配置"],
                    "summary_text": f"汇总失败：{str(e)}"
                }
            }